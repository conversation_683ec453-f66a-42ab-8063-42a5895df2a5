{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2983311369882895114, "build_script_build", false, 592235611495079263], [10755362358622467486, "build_script_build", false, 7439043014947341424], [12783828711503588811, "build_script_build", false, 17980643209073249502], [17509843537913359226, "build_script_build", false, 17721424096254657492], [1582828171158827377, "build_script_build", false, 17916824361942114591], [5943080732378436368, "build_script_build", false, 8096723362993470123], [11721252211900136025, "build_script_build", false, 11565704714141713044]], "local": [{"RerunIfChanged": {"output": "debug\\build\\cocos-initiator-4129719432409e34\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}