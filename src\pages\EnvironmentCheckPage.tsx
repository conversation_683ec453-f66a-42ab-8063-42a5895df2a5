"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { invoke } from "@tauri-apps/api/core"

interface EnvironmentStatus {
  git_installed: boolean
  git_version?: string
  nodejs_installed: boolean
  nodejs_version?: string
  npm_installed: boolean
  npm_version?: string
  all_ready: boolean
}

interface EnvironmentCheckPageProps {
  onEnvironmentReady: () => void
}

const EnvironmentCheckPage: React.FC<EnvironmentCheckPageProps> = ({ onEnvironmentReady }) => {
  const [envStatus, setEnvStatus] = useState<EnvironmentStatus | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkEnvironment = async () => {
    try {
      setIsChecking(true)
      setError(null)

      if (typeof window !== "undefined" && (window as any).__TAURI__) {
        const status = await invoke<EnvironmentStatus>("check_environment")
        setEnvStatus(status)

        // 如果环境完整，自动进入工作区
        if (status.all_ready) {
          setTimeout(() => {
            onEnvironmentReady()
          }, 1000)
        }
      } else {
        // 浏览器环境，模拟数据
        const mockStatus: EnvironmentStatus = {
          git_installed: true,
          git_version: "2.34.1",
          nodejs_installed: true,
          nodejs_version: "v18.17.0",
          npm_installed: true,
          npm_version: "9.6.7",
          all_ready: true,
        }
        setEnvStatus(mockStatus)
        setTimeout(() => {
          onEnvironmentReady()
        }, 1000)
      }
    } catch (error: any) {
      setError(error.message || "检查环境失败")
    } finally {
      setIsChecking(false)
    }
  }

  const openInstallationGuide = async (software: string) => {
    try {
      if (typeof window !== "undefined" && (window as any).__TAURI__) {
        await invoke("open_installation_guide", { software })
      } else {
        // 浏览器环境，直接打开链接
        const urls = {
          git: "https://git-scm.com/downloads",
          nodejs: "https://nodejs.org/",
        }
        window.open(urls[software as keyof typeof urls], "_blank")
      }
    } catch (error: any) {
      setError(error.message || "打开安装指南失败")
    }
  }

  useEffect(() => {
    checkEnvironment()
  }, [])

  const getStatusText = (installed: boolean) => {
    return installed ? "已安装" : "未安装"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/20 to-blue-600/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/50 p-8 w-full max-w-4xl">
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-2xl mb-6 shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent mb-4">
            环境检查
          </h1>
          <p className="text-gray-600 text-xl leading-relaxed max-w-2xl mx-auto">
            在使用 Cocos Initiator 之前，需要确保您的系统已安装必要的开发环境
          </p>
        </div>

        {error && (
          <div className="bg-gradient-to-r from-red-50 to-pink-50 backdrop-blur-sm border border-red-200/60 rounded-2xl p-6 mb-8 shadow-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-red-100 to-red-200 rounded-xl flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <span className="text-red-700 font-semibold text-lg">{error}</span>
            </div>
          </div>
        )}

        {isChecking ? (
          <div className="text-center py-16">
            <div className="relative mb-8">
              <div className="w-20 h-20 mx-auto">
                <div className="absolute inset-0 rounded-full border-4 border-gray-200"></div>
                <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-500 border-r-purple-500 animate-spin"></div>
              </div>
            </div>
            <h3 className="text-2xl font-bold text-gray-800 mb-3">正在检查环境...</h3>
            <p className="text-gray-600 text-lg">请稍候，这可能需要几秒钟</p>
          </div>
        ) : envStatus ? (
          <div className="space-y-6">
            {/* Git 检查 */}
            <div className="group relative overflow-hidden bg-gradient-to-r from-white/90 to-blue-50/50 rounded-2xl p-8 border border-blue-100/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <div
                    className={`flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 ${
                      envStatus.git_installed
                        ? "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
                        : "bg-gradient-to-r from-red-400 to-pink-500 text-white"
                    }`}
                  >
                    {envStatus.git_installed ? (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-2">Git</h3>
                    <p className="text-gray-600 text-lg">版本控制系统，用于管理项目代码</p>
                  </div>
                </div>
                <div className="text-right">
                  <div
                    className={`inline-flex items-center px-4 py-2 rounded-xl text-base font-semibold shadow-md ${
                      envStatus.git_installed
                        ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800"
                        : "bg-gradient-to-r from-red-100 to-pink-100 text-red-800"
                    }`}
                  >
                    {getStatusText(envStatus.git_installed)}
                  </div>
                  {envStatus.git_version && (
                    <p className="text-sm text-gray-500 mt-3 font-mono bg-gray-100 px-3 py-1 rounded-lg">
                      {envStatus.git_version}
                    </p>
                  )}
                  {!envStatus.git_installed && (
                    <button
                      onClick={() => openInstallationGuide("git")}
                      className="mt-4 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white font-semibold rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      安装指南
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Node.js 检查 */}
            <div className="group relative overflow-hidden bg-gradient-to-r from-white/90 to-green-50/50 rounded-2xl p-8 border border-green-100/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <div
                    className={`flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 ${
                      envStatus.nodejs_installed
                        ? "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
                        : "bg-gradient-to-r from-red-400 to-pink-500 text-white"
                    }`}
                  >
                    {envStatus.nodejs_installed ? (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800 mb-2">Node.js</h3>
                    <p className="text-gray-600 text-lg">JavaScript 运行环境，某些功能需要</p>
                  </div>
                </div>
                <div className="text-right">
                  <div
                    className={`inline-flex items-center px-4 py-2 rounded-xl text-base font-semibold shadow-md ${
                      envStatus.nodejs_installed
                        ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800"
                        : "bg-gradient-to-r from-red-100 to-pink-100 text-red-800"
                    }`}
                  >
                    {getStatusText(envStatus.nodejs_installed)}
                  </div>
                  {envStatus.nodejs_version && (
                    <p className="text-sm text-gray-500 mt-3 font-mono bg-gray-100 px-3 py-1 rounded-lg">
                      {envStatus.nodejs_version}
                    </p>
                  )}
                  {!envStatus.nodejs_installed && (
                    <button
                      onClick={() => openInstallationGuide("nodejs")}
                      className="mt-4 px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white font-semibold rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      安装指南
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* npm 检查 */}
            {envStatus.nodejs_installed && (
              <div className="group relative overflow-hidden bg-gradient-to-r from-white/90 to-orange-50/50 rounded-2xl p-8 border border-orange-100/50 shadow-lg hover:shadow-xl transition-all duration-500 hover:-translate-y-1">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500/5 to-yellow-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div
                      className={`flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg transition-all duration-300 ${
                        envStatus.npm_installed
                          ? "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
                          : "bg-gradient-to-r from-red-400 to-pink-500 text-white"
                      }`}
                    >
                      {envStatus.npm_installed ? (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">npm</h3>
                      <p className="text-gray-600 text-lg">Node.js 包管理器</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div
                      className={`inline-flex items-center px-4 py-2 rounded-xl text-base font-semibold shadow-md ${
                        envStatus.npm_installed
                          ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800"
                          : "bg-gradient-to-r from-red-100 to-pink-100 text-red-800"
                      }`}
                    >
                      {getStatusText(envStatus.npm_installed)}
                    </div>
                    {envStatus.npm_version && (
                      <p className="text-sm text-gray-500 mt-3 font-mono bg-gray-100 px-3 py-1 rounded-lg">
                        {envStatus.npm_version}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 总体状态 */}
            <div
              className={`mt-12 p-10 rounded-3xl border-2 shadow-2xl transition-all duration-700 ${
                envStatus.all_ready
                  ? "bg-gradient-to-r from-green-50 via-emerald-50 to-green-50 border-green-200/60"
                  : "bg-gradient-to-r from-orange-50 via-yellow-50 to-orange-50 border-orange-200/60"
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6">
                  <div
                    className={`flex-shrink-0 w-20 h-20 rounded-2xl flex items-center justify-center shadow-xl transition-all duration-500 ${
                      envStatus.all_ready
                        ? "bg-gradient-to-r from-green-400 to-emerald-500 text-white"
                        : "bg-gradient-to-r from-orange-400 to-yellow-500 text-white"
                    }`}
                  >
                    {envStatus.all_ready ? (
                      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    ) : (
                      <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                    )}
                  </div>
                  <div>
                    <h3 className="text-3xl font-bold text-gray-800 mb-3">环境状态</h3>
                    <p className="text-gray-700 text-xl leading-relaxed">
                      {envStatus.all_ready
                        ? "🎉 环境检查完成，可以开始使用 Cocos Initiator"
                        : "⚠️ 请安装缺失的软件后重新检查"}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  {envStatus.all_ready ? (
                    <div className="flex flex-col items-end space-y-3">
                      <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 rounded-2xl font-bold text-lg shadow-lg">
                        <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        环境就绪
                      </div>
                      <p className="text-base text-gray-600 animate-pulse">正在跳转到工作区...</p>
                    </div>
                  ) : (
                    <button
                      onClick={checkEnvironment}
                      className="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold text-lg rounded-2xl hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
                    >
                      <svg className="w-6 h-6 inline mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      重新检查
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 跳过按钮（仅在某些环境缺失时显示） */}
            {!envStatus.all_ready && (
              <div className="text-center mt-12">
                <div className="inline-flex flex-col items-center space-y-4">
                  <div className="w-px h-12 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                  <button
                    onClick={onEnvironmentReady}
                    className="group px-8 py-4 text-gray-700 border-2 border-gray-200 rounded-2xl hover:border-gray-300 hover:bg-gray-50 transition-all duration-300 font-semibold text-lg shadow-lg hover:shadow-xl"
                  >
                    <svg
                      className="w-6 h-6 inline mr-3 group-hover:text-orange-500 transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    跳过检查，继续使用
                  </button>
                  <p className="text-sm text-gray-500 max-w-sm leading-relaxed">⚠️ 跳过检查可能导致部分功能不可用</p>
                </div>
              </div>
            )}
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default EnvironmentCheckPage
