{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 15657897354478470176, "path": 8694853403362143863, "deps": [[947818755262499932, "notify_rust", false, 12162791815043296636], [3150220818285335163, "url", false, 7454335481254515655], [5986029879202738730, "log", false, 8555561636712512847], [9689903380558560274, "serde", false, 12420880179438694917], [10755362358622467486, "tauri", false, 16350496299621225213], [10806645703491011684, "thiserror", false, 3881699408214437866], [12409575957772518135, "time", false, 1277377757230374329], [12783828711503588811, "build_script_build", false, 17980643209073249502], [12986574360607194341, "serde_repr", false, 10890463440449661988], [13208667028893622512, "rand", false, 4526182786790451614], [15367738274754116744, "serde_json", false, 17720886181995014648]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-5c3c0a86c9e36ad6\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}