{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 367816849085071872, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 13602462539210999394], [1582828171158827377, "build_script_build", false, 6760108438678220053], [5986029879202738730, "log", false, 82691605009859048], [9451456094439810778, "regex", false, 14593913095806571781], [9538054652646069845, "tokio", false, 17242659437566319771], [9689903380558560274, "serde", false, 7153253516477134116], [10755362358622467486, "tauri", false, 4418156658079071460], [10806645703491011684, "thiserror", false, 5976101502267111473], [11337703028400419576, "os_pipe", false, 16588126229579103397], [14564311161534545801, "encoding_rs", false, 17070682502328113262], [15367738274754116744, "serde_json", false, 11919080132714597288], [16192041687293812804, "open", false, 18007430511685764492]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-shell-bad9c94d5def06ae\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}