{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 9903042276772267709], [442785307232013896, "tauri_runtime", false, 3954063056236084616], [1200537532907108615, "url<PERSON><PERSON>n", false, 1904767817637953682], [3150220818285335163, "url", false, 7454335481254515655], [4143744114649553716, "raw_window_handle", false, 2995522544373100678], [4341921533227644514, "muda", false, 5014994616046288140], [4919829919303820331, "serialize_to_javascript", false, 2986020920228468993], [5986029879202738730, "log", false, 8555561636712512847], [7752760652095876438, "tauri_runtime_wry", false, 6628914535992176375], [8539587424388551196, "webview2_com", false, 4618949674958641600], [9010263965687315507, "http", false, 13891704466643743123], [9228235415475680086, "tauri_macros", false, 5108134519695673369], [9538054652646069845, "tokio", false, 8982450163724697713], [9689903380558560274, "serde", false, 12420880179438694917], [9920160576179037441, "getrandom", false, 1588342528421934599], [10229185211513642314, "mime", false, 1852428248072666615], [10629569228670356391, "futures_util", false, 383300555520732793], [10755362358622467486, "build_script_build", false, 7439043014947341424], [10806645703491011684, "thiserror", false, 3881699408214437866], [11050281405049894993, "tauri_utils", false, 5826606491688714813], [11989259058781683633, "dunce", false, 10230067452156376648], [12565293087094287914, "window_vibrancy", false, 12048726663880459008], [12986574360607194341, "serde_repr", false, 10890463440449661988], [13077543566650298139, "heck", false, 2225206494654259885], [13116089016666501665, "windows", false, 15784846249245364666], [13625485746686963219, "anyhow", false, 9739582024938941695], [15367738274754116744, "serde_json", false, 17720886181995014648], [16928111194414003569, "dirs", false, 6106396076726663656], [17155886227862585100, "glob", false, 8808646221422140041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-adb3b0ef2ba3f735\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}