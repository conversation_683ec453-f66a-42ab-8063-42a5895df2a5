{"rustc": 16591470773350601817, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 1660944289106784126, "deps": [[3150220818285335163, "url", false, 13355888952511495717], [6913375703034175521, "build_script_build", false, 1199375982343897648], [8319709847752024821, "uuid1", false, 4738604866675565772], [9122563107207267705, "dyn_clone", false, 10976741586022471282], [9689903380558560274, "serde", false, 15168726803780673892], [14923790796823607459, "indexmap", false, 3113301373403573415], [15367738274754116744, "serde_json", false, 16047163076923800125], [16071897500792579091, "schemars_derive", false, 11125599575298955816]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-b4bf089e3244e064\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}