{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 1013487812369986955, "path": 11573281436853214370, "deps": [[40386456601120721, "percent_encoding", false, 12596436445136119614], [95042085696191081, "ipnet", false, 12307163991837437194], [784494742817713399, "tower_service", false, 15964911012758105550], [970965535607393401, "hyper_util", false, 1375367374775720896], [1288403060204016458, "tokio_util", false, 5336018126010820687], [1475350004718460454, "tower_http", false, 17948238000503124610], [1788832197870803419, "hyper_rustls", false, 5869720290062579663], [1811549171721445101, "futures_channel", false, 5359583996229553453], [1906322745568073236, "pin_project_lite", false, 13311195618776491033], [2517136641825875337, "sync_wrapper", false, 14578260832044194474], [2883436298747778685, "rustls_pki_types", false, 15169120760884172430], [3150220818285335163, "url", false, 17986292695802632123], [3722963349756955755, "once_cell", false, 4139169250953353585], [4942430025333810336, "webpki_roots", false, 3683460184814857450], [5695049318159433696, "tower", false, 5964969182755890320], [5986029879202738730, "log", false, 82691605009859048], [7161480121686072451, "rustls", false, 6416754387116556532], [7620660491849607393, "futures_core", false, 3818723815904826072], [9010263965687315507, "http", false, 7923552186516675881], [9538054652646069845, "tokio", false, 17242659437566319771], [9689903380558560274, "serde", false, 7153253516477134116], [10229185211513642314, "mime", false, 1622292474083181368], [10629569228670356391, "futures_util", false, 1396026813022400302], [11895591994124935963, "tokio_rustls", false, 6975691956312171214], [11957360342995674422, "hyper", false, 17539860189617805779], [13077212702700853852, "base64", false, 14548208607392333621], [14084095096285906100, "http_body", false, 6708585730689562682], [15367738274754116744, "serde_json", false, 11919080132714597288], [16066129441945555748, "bytes", false, 2661020119886165922], [16542808166767769916, "serde_urlencoded", false, 6460584385547132065], [16900715236047033623, "http_body_util", false, 5206252916396756323]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-ba38f7d3ac93ac79\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}