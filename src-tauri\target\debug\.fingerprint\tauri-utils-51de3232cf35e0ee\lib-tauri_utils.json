{"rustc": 16591470773350601817, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 10234294097778478786], [1200537532907108615, "url<PERSON><PERSON>n", false, 5608197939917070908], [3060637413840920116, "proc_macro2", false, 3722225902823197376], [3129130049864710036, "memchr", false, 8273705717749281549], [3150220818285335163, "url", false, 13355888952511495717], [3191507132440681679, "serde_untagged", false, 4945943875115499360], [4899080583175475170, "semver", false, 5608513836075258160], [5986029879202738730, "log", false, 8555561636712512847], [6213549728662707793, "serde_with", false, 4401241435527360368], [6262254372177975231, "kuchiki", false, 5976313476653633027], [6606131838865521726, "ctor", false, 13122526423855299229], [6913375703034175521, "schemars", false, 11519239817704840179], [7170110829644101142, "json_patch", false, 6566727955510569424], [8319709847752024821, "uuid", false, 4738604866675565772], [8786711029710048183, "toml", false, 12089303468504858911], [9010263965687315507, "http", false, 13891704466643743123], [9451456094439810778, "regex", false, 3815492220500898872], [9689903380558560274, "serde", false, 15168726803780673892], [10806645703491011684, "thiserror", false, 3881699408214437866], [11655476559277113544, "cargo_metadata", false, 9346198095890995507], [11989259058781683633, "dunce", false, 10230067452156376648], [13625485746686963219, "anyhow", false, 9739582024938941695], [14132538657330703225, "brotli", false, 4246135052404262665], [15367738274754116744, "serde_json", false, 16047163076923800125], [15622660310229662834, "walkdir", false, 11931754989844931061], [17146114186171651583, "infer", false, 4038272381533214278], [17155886227862585100, "glob", false, 8808646221422140041], [17186037756130803222, "phf", false, 10266854175166896412], [17990358020177143287, "quote", false, 15042609247914122772]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-51de3232cf35e0ee\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}