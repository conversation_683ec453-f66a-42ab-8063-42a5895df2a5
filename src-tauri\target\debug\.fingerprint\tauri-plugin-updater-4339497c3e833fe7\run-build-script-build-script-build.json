{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 7439043014947341424], [11721252211900136025, "build_script_build", false, 3100767328626419070]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-4339497c3e833fe7\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}