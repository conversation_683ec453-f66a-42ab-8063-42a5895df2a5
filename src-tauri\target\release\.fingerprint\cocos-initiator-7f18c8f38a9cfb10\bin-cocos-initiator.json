{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 16679894901332367203, "profile": 14376581640584361896, "path": 4942398508502643691, "deps": [[1143317734563568576, "reqwest", false, 7320968099127449854], [1582828171158827377, "tauri_plugin_shell", false, 12450184483316441928], [2983311369882895114, "cocos_initiator_lib", false, 8336971972634494350], [2983311369882895114, "build_script_build", false, 13986632813059871475], [5943080732378436368, "tauri_plugin_store", false, 18047785457596365236], [9451456094439810778, "regex", false, 14593913095806571781], [9538054652646069845, "tokio", false, 17242659437566319771], [9689903380558560274, "serde", false, 7153253516477134116], [9897246384292347999, "chrono", false, 14501265362424155668], [9963614578868468249, "sysinfo", false, 14432493906156519102], [10755362358622467486, "tauri", false, 4418156658079071460], [11721252211900136025, "tauri_plugin_updater", false, 12355833991333390844], [12783828711503588811, "tauri_plugin_notification", false, 7109132773979555517], [13625485746686963219, "anyhow", false, 2115109158373166646], [15367738274754116744, "serde_json", false, 11919080132714597288], [17509843537913359226, "tauri_plugin_process", false, 10079221971161626524]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\cocos-initiator-7f18c8f38a9cfb10\\dep-bin-cocos-initiator", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}