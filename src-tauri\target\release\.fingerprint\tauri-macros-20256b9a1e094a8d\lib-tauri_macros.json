{"rustc": 16591470773350601817, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 17984201634715228204, "path": 8778928345189965635, "deps": [[3060637413840920116, "proc_macro2", false, 11804710092911423844], [7341521034400937459, "tauri_codegen", false, 5175006475586745440], [11050281405049894993, "tauri_utils", false, 4884743590597463402], [13077543566650298139, "heck", false, 12378107339233081810], [17990358020177143287, "quote", false, 8139606416633743329], [18149961000318489080, "syn", false, 13272221048175673847]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-20256b9a1e094a8d\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}