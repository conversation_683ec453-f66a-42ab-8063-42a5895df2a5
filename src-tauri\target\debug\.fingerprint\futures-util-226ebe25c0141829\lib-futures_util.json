{"rustc": 16591470773350601817, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 13318305459243126790, "path": 3303257178207977090, "deps": [[5103565458935487, "futures_io", false, 10184469707965089326], [1615478164327904835, "pin_utils", false, 12096768787333181728], [1906322745568073236, "pin_project_lite", false, 5517959609193328240], [3129130049864710036, "memchr", false, 8273705717749281549], [6955678925937229351, "slab", false, 6694329926550020988], [7013762810557009322, "futures_sink", false, 17145818316762017606], [7620660491849607393, "futures_core", false, 15145191665480823398], [10565019901765856648, "futures_macro", false, 18130181254088455073], [16240732885093539806, "futures_task", false, 15605073868113214427]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-226ebe25c0141829\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}