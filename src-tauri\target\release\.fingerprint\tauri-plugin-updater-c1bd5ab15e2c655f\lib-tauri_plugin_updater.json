{"rustc": 16591470773350601817, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 367816849085071872, "path": 6899552628466203349, "deps": [[40386456601120721, "percent_encoding", false, 12596436445136119614], [1143317734563568576, "reqwest", false, 7320968099127449854], [1441306149310335789, "tempfile", false, 2804164810730032342], [3150220818285335163, "url", false, 17986292695802632123], [4899080583175475170, "semver", false, 8144346282259296987], [5986029879202738730, "log", false, 82691605009859048], [9010263965687315507, "http", false, 7923552186516675881], [9332307739160395223, "minisign_verify", false, 11637113981605309121], [9538054652646069845, "tokio", false, 17242659437566319771], [9689903380558560274, "serde", false, 7153253516477134116], [10281541584571964250, "windows_sys", false, 2787006813638911836], [10629569228670356391, "futures_util", false, 1396026813022400302], [10755362358622467486, "tauri", false, 4418156658079071460], [10806645703491011684, "thiserror", false, 5976101502267111473], [11721252211900136025, "build_script_build", false, 9817109601732795452], [12409575957772518135, "time", false, 15581143489650758892], [13077212702700853852, "base64", false, 14548208607392333621], [15367738274754116744, "serde_json", false, 11919080132714597288], [17146114186171651583, "infer", false, 8011255992211451020], [18372475104564266000, "zip", false, 8852383308788941362]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-updater-c1bd5ab15e2c655f\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}