{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 367816849085071872, "path": 5451450293158687294, "deps": [[1462335029370885857, "quick_xml", false, 5494537658685776020], [3334271191048661305, "windows_version", false, 9911059258184822592], [10806645703491011684, "thiserror", false, 5976101502267111473], [13116089016666501665, "windows", false, 12252872986836496617]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winrt-notification-3cabebcaa4851649\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}