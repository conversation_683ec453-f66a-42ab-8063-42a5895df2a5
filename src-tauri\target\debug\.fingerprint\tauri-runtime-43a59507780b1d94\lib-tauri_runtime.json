{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 7481791422461864518], [3150220818285335163, "url", false, 7454335481254515655], [4143744114649553716, "raw_window_handle", false, 2995522544373100678], [7606335748176206944, "dpi", false, 9060350174135931069], [9010263965687315507, "http", false, 13891704466643743123], [9689903380558560274, "serde", false, 12420880179438694917], [10806645703491011684, "thiserror", false, 3881699408214437866], [11050281405049894993, "tauri_utils", false, 5826606491688714813], [13116089016666501665, "windows", false, 15784846249245364666], [15367738274754116744, "serde_json", false, 17720886181995014648], [16727543399706004146, "cookie", false, 11349485460696896666]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-43a59507780b1d94\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}