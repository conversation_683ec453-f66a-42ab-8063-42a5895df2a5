{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 5290030462671737236, "path": 11805383375341948447, "deps": [[6158493786865284961, "serde_with_macros", false, 10484928283395342827], [9689903380558560274, "serde", false, 15168726803780673892], [16257276029081467297, "serde_derive", false, 9541645822996390631]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-21bc3e23139d5530\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}