{"rustc": 16591470773350601817, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5081136802505358982, "profile": 15657897354478470176, "path": 6899552628466203349, "deps": [[40386456601120721, "percent_encoding", false, 9903042276772267709], [1143317734563568576, "reqwest", false, 9878154433759480323], [1441306149310335789, "tempfile", false, 7276870239756350954], [3150220818285335163, "url", false, 7454335481254515655], [4899080583175475170, "semver", false, 8574770502769654316], [5986029879202738730, "log", false, 8555561636712512847], [9010263965687315507, "http", false, 13891704466643743123], [9332307739160395223, "minisign_verify", false, 13947034720459239227], [9538054652646069845, "tokio", false, 8982450163724697713], [9689903380558560274, "serde", false, 12420880179438694917], [10281541584571964250, "windows_sys", false, 7247288348270788477], [10629569228670356391, "futures_util", false, 383300555520732793], [10755362358622467486, "tauri", false, 16350496299621225213], [10806645703491011684, "thiserror", false, 3881699408214437866], [11721252211900136025, "build_script_build", false, 11565704714141713044], [12409575957772518135, "time", false, 1277377757230374329], [13077212702700853852, "base64", false, 13674227561419837107], [15367738274754116744, "serde_json", false, 17720886181995014648], [17146114186171651583, "infer", false, 3972285143878144806], [18372475104564266000, "zip", false, 8761054650635321256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-409da327efb2196a\\dep-lib-tauri_plugin_updater", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}