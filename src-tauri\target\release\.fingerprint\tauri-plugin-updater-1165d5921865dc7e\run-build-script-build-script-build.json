{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 12242051209942776525], [11721252211900136025, "build_script_build", false, 5810329229744500117]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-updater-1165d5921865dc7e\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}