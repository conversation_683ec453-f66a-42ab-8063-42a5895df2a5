{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 367816849085071872, "path": 8694853403362143863, "deps": [[947818755262499932, "notify_rust", false, 17944952054147201299], [3150220818285335163, "url", false, 17986292695802632123], [5986029879202738730, "log", false, 82691605009859048], [9689903380558560274, "serde", false, 7153253516477134116], [10755362358622467486, "tauri", false, 4418156658079071460], [10806645703491011684, "thiserror", false, 5976101502267111473], [12409575957772518135, "time", false, 15581143489650758892], [12783828711503588811, "build_script_build", false, 6989816878674569119], [12986574360607194341, "serde_repr", false, 7748733700478918286], [13208667028893622512, "rand", false, 18296353636463580660], [15367738274754116744, "serde_json", false, 11919080132714597288]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-notification-81a7b3aaa1f7bd32\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}