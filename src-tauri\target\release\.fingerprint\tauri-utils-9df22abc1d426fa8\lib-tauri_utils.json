{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 367816849085071872, "path": 12518800248535767337, "deps": [[561782849581144631, "html5ever", false, 6122852806095552021], [1200537532907108615, "url<PERSON><PERSON>n", false, 3759044069293431078], [3129130049864710036, "memchr", false, 5921999988612687598], [3150220818285335163, "url", false, 17986292695802632123], [3191507132440681679, "serde_untagged", false, 7224065445431789969], [4899080583175475170, "semver", false, 8144346282259296987], [5986029879202738730, "log", false, 82691605009859048], [6213549728662707793, "serde_with", false, 3302638390852760819], [6262254372177975231, "kuchiki", false, 12434320022952294556], [6606131838865521726, "ctor", false, 13615880286957296067], [7170110829644101142, "json_patch", false, 8055033410073499157], [8319709847752024821, "uuid", false, 18340854517338696949], [8786711029710048183, "toml", false, 1016292444105330541], [9010263965687315507, "http", false, 7923552186516675881], [9451456094439810778, "regex", false, 14593913095806571781], [9689903380558560274, "serde", false, 7153253516477134116], [10806645703491011684, "thiserror", false, 5976101502267111473], [11989259058781683633, "dunce", false, 2495506939808963512], [13625485746686963219, "anyhow", false, 2115109158373166646], [14132538657330703225, "brotli", false, 2936409583406662466], [15367738274754116744, "serde_json", false, 11919080132714597288], [15622660310229662834, "walkdir", false, 14444516584352356743], [17146114186171651583, "infer", false, 8011255992211451020], [17155886227862585100, "glob", false, 5391393751989952526], [17186037756130803222, "phf", false, 11835360022162718058]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-9df22abc1d426fa8\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}