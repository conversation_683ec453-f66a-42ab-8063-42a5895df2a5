import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import LoginPage from './pages/LoginPage';
import SettingsPage from './pages/SettingsPage';
import WorkspacePage from './pages/WorkspacePage';
import CocosManagerPage from './pages/CocosManagerPage';
import EnvironmentCheckPage from './pages/EnvironmentCheckPage';
import QuickLoginPage from './components/QuickLoginPage';
import SystemTrayManager from './components/SystemTrayManager';
import UpdateManager from './components/UpdateManager';
import { clearNotificationHistory } from './components/NotificationManager';

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const [hasStoredCredentials, setHasStoredCredentials] = useState(false);
  const [showFullLogin, setShowFullLogin] = useState(false);
  const [environmentChecked, setEnvironmentChecked] = useState(false);
  const [showEnvironmentCheck, setShowEnvironmentCheck] = useState(false);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        // Check if we're in Tauri environment
        if (typeof window !== 'undefined' && window.__TAURI__) {
          const credentials = await invoke('load_gitea_credentials');
          if (credentials) {
            setHasStoredCredentials(true);
            // 不自动登录，让用户选择
          } else {
            setHasStoredCredentials(false);
            setShowFullLogin(true);
          }
        } else {
          // In browser environment, check localStorage for demo credentials
          console.log('Running in browser environment, checking localStorage');
          const storedCredentials = localStorage.getItem('demo_credentials');
          if (storedCredentials) {
            setHasStoredCredentials(true);
            // 不自动登录，让用户选择
          } else {
            setHasStoredCredentials(false);
            setShowFullLogin(true);
          }
        }
      } catch (err) {
        console.error('检查认证状态失败:', err);
        setIsAuthenticated(false);
        setShowFullLogin(true);
      } finally {
        setIsAuthLoading(false);
      }
    };
    checkAuthStatus();
  }, []);

  const handleLogin = () => {
    setIsAuthenticated(true);
    setShowEnvironmentCheck(true); // 登录后显示环境检查
    // 在浏览器环境中保存登录状态到localStorage
    if (typeof window !== 'undefined' && !window.__TAURI__) {
      localStorage.setItem('demo_credentials', JSON.stringify({
        account: 'demo',
        timestamp: Date.now()
      }));
    }
  };

  const handleEnvironmentReady = () => {
    setEnvironmentChecked(true);
    setShowEnvironmentCheck(false);
  };

  const handleLogout = async () => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('clear_gitea_credentials');
      } else {
        // 在浏览器环境中清除localStorage
        localStorage.removeItem('demo_credentials');
      }

      // 清除通知历史记录
      clearNotificationHistory();

      setIsAuthenticated(false);
      setHasStoredCredentials(false);
      setShowFullLogin(true);
      setEnvironmentChecked(false);
      setShowEnvironmentCheck(false);
    } catch (err) {
      console.error('注销失败:', err);
    }
  };

  if (isAuthLoading) {
    return <div>加载认证状态...</div>; // 或者一个加载指示器
  }

  const renderLoginComponent = () => {
    if (hasStoredCredentials && !showFullLogin) {
      return (
        <QuickLoginPage
          onLogin={handleLogin}
          onShowFullLogin={() => setShowFullLogin(true)}
        />
      );
    } else {
      return <LoginPage onLogin={handleLogin} />;
    }
  };

  return (
    <Router>
      <SystemTrayManager isAuthenticated={isAuthenticated} />
      <UpdateManager isAuthenticated={isAuthenticated} />
      <Routes>
        <Route
          path="/"
          element={
            isAuthenticated ? (
              showEnvironmentCheck ? (
                <EnvironmentCheckPage onEnvironmentReady={handleEnvironmentReady} />
              ) : (
                <Navigate to="/workspace" />
              )
            ) : (
              renderLoginComponent()
            )
          }
        />
        <Route
          path="/settings"
          element={
            isAuthenticated ? (
              environmentChecked ? (
                <SettingsPage onLogout={handleLogout} />
              ) : (
                <Navigate to="/" />
              )
            ) : (
              <Navigate to="/" />
            )
          }
        />
        <Route
          path="/workspace"
          element={
            isAuthenticated ? (
              environmentChecked ? (
                <WorkspacePage onLogout={handleLogout} />
              ) : (
                <Navigate to="/" />
              )
            ) : (
              <Navigate to="/" />
            )
          }
        />
        <Route
          path="/cocos-manager"
          element={
            isAuthenticated ? (
              environmentChecked ? (
                <CocosManagerPage onLogout={handleLogout} />
              ) : (
                <Navigate to="/" />
              )
            ) : (
              <Navigate to="/" />
            )
          }
        />
      </Routes>
    </Router>
  );
};

export default App;
