{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 12242051209942776525], [1582828171158827377, "build_script_build", false, 10948222076049296390]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-shell-ccb092c18c7e1b86\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}