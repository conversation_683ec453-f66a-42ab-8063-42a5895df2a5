cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=E:\share\code\cocos-Initiator\src-tauri\target\debug\build\tauri-plugin-store-1a97c56a67ba70e9\out\tauri-plugin-store-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-store-2.2.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
