{"rustc": 16591470773350601817, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 3856213520004275544, "deps": [[784494742817713399, "tower_service", false, 4279352996323888024], [1906322745568073236, "pin_project_lite", false, 5517959609193328240], [4121350475192885151, "iri_string", false, 7045396451954008867], [5695049318159433696, "tower", false, 12795875158302883296], [7712452662827335977, "tower_layer", false, 1774617626234245612], [7896293946984509699, "bitflags", false, 383822116282084376], [9010263965687315507, "http", false, 13891704466643743123], [10629569228670356391, "futures_util", false, 383300555520732793], [14084095096285906100, "http_body", false, 10916235814274115685], [16066129441945555748, "bytes", false, 11196354275054407066]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-eddedf40265ea86e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}