["\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\tauri-9f996d1b746da8d6\\out\\permissions\\menu\\autogenerated\\default.toml"]