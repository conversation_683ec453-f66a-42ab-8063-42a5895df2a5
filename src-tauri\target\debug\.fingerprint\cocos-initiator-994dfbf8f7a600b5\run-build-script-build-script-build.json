{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2983311369882895114, "build_script_build", false, 7945865216125958961], [10755362358622467486, "build_script_build", false, 5048490967110359917], [12783828711503588811, "build_script_build", false, 8667156130679017880], [17509843537913359226, "build_script_build", false, 11804820850133664239], [1582828171158827377, "build_script_build", false, 885769539630316907], [5943080732378436368, "build_script_build", false, 2223836141800612455], [11721252211900136025, "build_script_build", false, 10340376415565449355]], "local": [{"RerunIfChanged": {"output": "debug\\build\\cocos-initiator-994dfbf8f7a600b5\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}