E:\share\code\cocos-Initiator\src-tauri\target\debug\deps\libcocos_initiator-12521a708b8cfca9.rmeta: src\main.rs src\auth.rs E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

E:\share\code\cocos-Initiator\src-tauri\target\debug\deps\cocos_initiator-12521a708b8cfca9.d: src\main.rs src\auth.rs E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\main.rs:
src\auth.rs:
E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=cocos-initiator
# env-dep:OUT_DIR=E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\cocos-initiator-994dfbf8f7a600b5\\out
