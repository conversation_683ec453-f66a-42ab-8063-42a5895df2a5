{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 367816849085071872, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 13714393061649945759], [442785307232013896, "tauri_runtime", false, 8356745966971955152], [3150220818285335163, "url", false, 17986292695802632123], [3722963349756955755, "once_cell", false, 4139169250953353585], [4143744114649553716, "raw_window_handle", false, 14973095541760575874], [5986029879202738730, "log", false, 82691605009859048], [7752760652095876438, "build_script_build", false, 9819072899746128492], [8539587424388551196, "webview2_com", false, 327719958832832374], [9010263965687315507, "http", false, 7923552186516675881], [11050281405049894993, "tauri_utils", false, 16794711885998847944], [13116089016666501665, "windows", false, 12252872986836496617], [13223659721939363523, "tao", false, 14354569625105424102], [14794439852947137341, "wry", false, 17836831540222952114]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-134b909b09c1659a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}