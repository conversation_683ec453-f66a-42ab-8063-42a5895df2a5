{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 367816849085071872, "path": 1288308961366000512, "deps": [[442785307232013896, "build_script_build", false, 6195081558422997478], [3150220818285335163, "url", false, 17986292695802632123], [4143744114649553716, "raw_window_handle", false, 14973095541760575874], [7606335748176206944, "dpi", false, 18290570192443178057], [9010263965687315507, "http", false, 7923552186516675881], [9689903380558560274, "serde", false, 7153253516477134116], [10806645703491011684, "thiserror", false, 5976101502267111473], [11050281405049894993, "tauri_utils", false, 16794711885998847944], [13116089016666501665, "windows", false, 12252872986836496617], [15367738274754116744, "serde_json", false, 11919080132714597288], [16727543399706004146, "cookie", false, 2227030224445706818]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-9d8b12a4018a808a\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}