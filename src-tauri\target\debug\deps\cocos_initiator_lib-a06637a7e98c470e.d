E:\share\code\cocos-Initiator\src-tauri\target\debug\deps\libcocos_initiator_lib-a06637a7e98c470e.rmeta: src\lib.rs E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

E:\share\code\cocos-Initiator\src-tauri\target\debug\deps\cocos_initiator_lib-a06637a7e98c470e.d: src\lib.rs E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
E:\share\code\cocos-Initiator\src-tauri\target\debug\build\cocos-initiator-994dfbf8f7a600b5\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=cocos-initiator
# env-dep:OUT_DIR=E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\debug\\build\\cocos-initiator-994dfbf8f7a600b5\\out
