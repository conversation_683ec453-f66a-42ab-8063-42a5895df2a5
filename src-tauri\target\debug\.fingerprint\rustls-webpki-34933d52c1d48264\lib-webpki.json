{"rustc": 16591470773350601817, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 12590007092919164603, "deps": [[2883436298747778685, "pki_types", false, 3326270106593689579], [5491919304041016563, "ring", false, 17467581341565895108], [8995469080876806959, "untrusted", false, 10336412172648289497]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-34933d52c1d48264\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}