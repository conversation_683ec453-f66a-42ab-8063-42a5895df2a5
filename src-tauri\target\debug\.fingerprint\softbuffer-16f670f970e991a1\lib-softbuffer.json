{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 4872535670476380077, "deps": [[376837177317575824, "build_script_build", false, 13220045413837392344], [4143744114649553716, "raw_window_handle", false, 2995522544373100678], [5986029879202738730, "log", false, 8555561636712512847], [10281541584571964250, "windows_sys", false, 7247288348270788477]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-16f670f970e991a1\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}