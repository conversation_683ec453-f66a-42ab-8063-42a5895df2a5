import { useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

// 本地存储键名
const PREVIOUS_PROJECTS_KEY = 'cocos_initiator_previous_projects';
const CLONE_OPERATION_KEY = 'cocos_initiator_clone_operation';
const WELCOME_SHOWN_KEY = 'cocos_initiator_welcome_shown';

// 获取之前的项目列表
const getPreviousProjects = (): Project[] => {
  try {
    const stored = localStorage.getItem(PREVIOUS_PROJECTS_KEY);
    return stored ? JSON.parse(stored) : [];
  } catch {
    return [];
  }
};

// 保存当前项目列表
const savePreviousProjects = (projects: Project[]) => {
  try {
    localStorage.setItem(PREVIOUS_PROJECTS_KEY, JSON.stringify(projects));
  } catch (error) {
    console.error('保存项目列表失败:', error);
  }
};

// 标记克隆操作（在克隆时调用）
export const markCloneOperation = (projectId: string) => {
  try {
    localStorage.setItem(CLONE_OPERATION_KEY, JSON.stringify({
      projectId,
      timestamp: Date.now()
    }));
  } catch (error) {
    console.error('标记克隆操作失败:', error);
  }
};

// 检查是否是克隆操作
const isCloneOperation = (projectId: string): boolean => {
  try {
    const stored = localStorage.getItem(CLONE_OPERATION_KEY);
    if (!stored) return false;

    const operation = JSON.parse(stored);
    // 检查是否是同一个项目且在5秒内
    if (operation.projectId === projectId && (Date.now() - operation.timestamp) < 5000) {
      // 清除标记，避免重复使用
      localStorage.removeItem(CLONE_OPERATION_KEY);
      return true;
    }
    return false;
  } catch {
    return false;
  }
};

// 检查是否已显示欢迎消息
const isWelcomeShown = (): boolean => {
  try {
    const stored = localStorage.getItem(WELCOME_SHOWN_KEY);
    return stored === 'true';
  } catch {
    return false;
  }
};

// 标记已显示欢迎消息
const markWelcomeShown = () => {
  try {
    localStorage.setItem(WELCOME_SHOWN_KEY, 'true');
  } catch (error) {
    console.error('标记欢迎消息失败:', error);
  }
};

// 清除通知记录（登出时调用）
export const clearNotificationHistory = () => {
  try {
    localStorage.removeItem(PREVIOUS_PROJECTS_KEY);
    localStorage.removeItem(CLONE_OPERATION_KEY);
    localStorage.removeItem(WELCOME_SHOWN_KEY);
  } catch (error) {
    console.error('清除通知记录失败:', error);
  }
};

interface Project {
  id: string;
  name: string;
  path: string;
  status: string;
  last_commit_time: string;
  repository_url: string;
  cocos_version?: string;
  process_id?: number;
}

interface NotificationManagerProps {
  projects: Project[];
  isAuthenticated: boolean;
}

const NotificationManager: React.FC<NotificationManagerProps> = ({
  projects,
  isAuthenticated
}) => {
  useEffect(() => {
    if (!isAuthenticated) return;

    const previousProjects = getPreviousProjects();

    // 如果是首次登录且有项目，显示欢迎消息
    if (previousProjects.length === 0 && projects.length > 0 && !isWelcomeShown()) {
      handleWelcomeMessage(projects.length);
      markWelcomeShown();
    }

    // 检查项目状态变化
    projects.forEach(currentProject => {
      const previousProject = previousProjects.find((p: Project) => p.id === currentProject.id);

      if (previousProject) {
        // 检查状态变化
        if (previousProject.status !== currentProject.status) {
          handleStatusChange(currentProject, previousProject.status, currentProject.status);
        }
      } else {
        // 新项目添加 - 只有克隆操作才发送"已添加"通知
        if (isCloneOperation(currentProject.id)) {
          handleNewProject(currentProject);
        }
      }
    });

    // 检查项目移除
    previousProjects.forEach((previousProject: Project) => {
      const currentProject = projects.find(p => p.id === previousProject.id);
      if (!currentProject) {
        handleProjectRemoved(previousProject);
      }
    });

    // 保存当前项目列表
    savePreviousProjects(projects);
  }, [projects, isAuthenticated]);

  const handleStatusChange = async (project: Project, oldStatus: string, newStatus: string) => {
    let title = '';
    let message = '';

    switch (newStatus) {
      case 'running':
        title = 'Cocos Creator 已启动';
        message = `项目 "${project.name}" 已在 Cocos Creator 中打开`;
        break;
      case 'closed':
        if (oldStatus === 'running') {
          title = 'Cocos Creator 已关闭';
          message = `项目 "${project.name}" 的 Cocos Creator 已关闭`;
        }
        break;
      case 'opened_folder':
        title = '项目文件夹已打开';
        message = `项目 "${project.name}" 的文件夹已在文件管理器中打开`;
        break;
      default:
        return; // 不发送通知
    }

    if (title && message) {
      await sendNotification(title, message);
    }
  };

  const handleWelcomeMessage = async (projectCount: number) => {
    const title = '欢迎回来！';
    const message = `您有 ${projectCount} 个项目在工作区中`;
    await sendNotification(title, message);
  };

  const handleNewProject = async (project: Project) => {
    const title = '新项目已添加';
    const message = `项目 "${project.name}" 已成功克隆并添加到工作区`;
    await sendNotification(title, message);
  };

  const handleProjectRemoved = async (project: Project) => {
    const title = '项目已移除';
    const message = `项目 "${project.name}" 已从工作区移除`;
    await sendNotification(title, message);
  };

  const sendNotification = async (title: string, body: string) => {
    try {
      if (typeof window !== 'undefined' && window.__TAURI__) {
        await invoke('send_notification', { title, body });
      } else {
        // 浏览器环境使用 Web Notification API
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(title, { body });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
          const permission = await Notification.requestPermission();
          if (permission === 'granted') {
            new Notification(title, { body });
          }
        }
      }
    } catch (error) {
      console.error('发送通知失败:', error);
    }
  };

  // 这个组件不渲染任何UI
  return null;
};

export default NotificationManager;
