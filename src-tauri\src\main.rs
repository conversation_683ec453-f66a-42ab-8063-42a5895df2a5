// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use anyhow::Result;
use std::sync::Mutex;
use tauri::{
    AppHandle, CustomMenuItem, Manager, State, SystemTray, SystemTrayEvent, SystemTrayMenu,
    SystemTrayMenuItem,
};

mod auth; // 引入auth模块
use auth::{clear_gitea_credentials, gitea_login_command, load_gitea_credentials};

use regex::Regex;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::process::{Child, Command};

#[derive(Serialize, Deserialize, Clone)]
pub struct AppSettings {
    pub auto_start: bool,
    pub notifications: bool,
    pub notification_type: String, // "none", "wechat", "feishu", "qq"
    pub wechat_config: String,
    pub feishu_config: String,
    pub qq_config: String,
    pub work_path: String,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct GiteaRepository {
    pub id: i64,
    pub name: String,
    pub full_name: String,
    pub description: Option<String>,
    pub clone_url: String,
    pub ssh_url: String,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub path: String,
    pub status: String, // "running" | "closed"
    pub last_commit_time: String,
    pub repository_url: String,
    pub cocos_version: Option<String>, // Cocos Creator版本
    pub process_id: Option<u32>,       // 进程ID
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AppConfig {
    pub gitea_api_base_url: String,
}

pub struct AppState {
    pub config: Mutex<AppConfig>,
    pub running_processes: Mutex<HashMap<String, Child>>, // 项目ID -> 进程
}

#[derive(Serialize, Deserialize, Clone)]
pub struct CocosCreatorInfo {
    pub version: String,
    pub path: String,
    pub executable: String,
}

#[derive(Serialize, Deserialize, Clone)]
pub struct EnvironmentStatus {
    pub git_installed: bool,
    pub git_version: Option<String>,
    pub nodejs_installed: bool,
    pub nodejs_version: Option<String>,
    pub npm_installed: bool,
    pub npm_version: Option<String>,
    pub all_ready: bool,
}

#[tauri::command]
async fn load_settings(app_handle: tauri::AppHandle) -> Result<AppSettings, String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;
    let settings_path = app_dir.join("settings.json");

    if !settings_path.exists() {
        return Ok(AppSettings {
            auto_start: false,
            notifications: false,
            notification_type: "none".to_string(),
            wechat_config: "".to_string(),
            feishu_config: "".to_string(),
            qq_config: "".to_string(),
            work_path: "".to_string(),
        });
    }

    let settings_json =
        fs::read_to_string(&settings_path).map_err(|e| format!("读取设置失败: {}", e))?;

    let settings: AppSettings =
        serde_json::from_str(&settings_json).map_err(|e| format!("解析设置失败: {}", e))?;

    Ok(settings)
}

#[tauri::command]
async fn save_settings(app_handle: tauri::AppHandle, settings: AppSettings) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;
    let settings_path = app_dir.join("settings.json");

    // 确保目录存在
    if let Some(parent) = settings_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }

    let settings_json =
        serde_json::to_string_pretty(&settings).map_err(|e| format!("序列化设置失败: {}", e))?;

    fs::write(&settings_path, settings_json).map_err(|e| format!("保存设置失败: {}", e))?;

    Ok(())
}

#[tauri::command]
async fn select_folder() -> Result<String, String> {
    use tauri::api::dialog::FileDialogBuilder;

    let result = FileDialogBuilder::new()
        .set_title("选择工作路径")
        .pick_folder();

    match result {
        Some(path) => Ok(path.to_string_lossy().into_owned()),
        None => Err("用户取消选择".into()),
    }
}

#[tauri::command]
async fn load_app_config(app_state: State<'_, AppState>) -> Result<AppConfig, String> {
    let config = app_state.config.lock().unwrap();
    Ok(config.clone())
}

#[tauri::command]
async fn fetch_gitea_repositories(
    app_handle: tauri::AppHandle,
    app_state: State<'_, AppState>,
) -> Result<Vec<GiteaRepository>, String> {
    // 获取存储的凭证
    let credentials = match auth::load_gitea_credentials(app_handle).await {
        Ok(Some(creds)) => creds,
        Ok(None) => return Err("未找到登录凭证".to_string()),
        Err(e) => return Err(format!("加载凭证失败: {}", e)),
    };

    let config = app_state.config.lock().unwrap();
    let client = Client::new();

    // 使用token获取用户的仓库列表
    let url = format!("{}/user/repos", config.gitea_api_base_url);

    let response = client
        .get(&url)
        .header("Authorization", format!("token {}", credentials.token))
        .send()
        .await
        .map_err(|e| format!("请求Gitea API失败: {}", e))?;

    if response.status().is_success() {
        let repos: Vec<GiteaRepository> = response
            .json()
            .await
            .map_err(|e| format!("解析仓库列表失败: {}", e))?;
        Ok(repos)
    } else {
        let error_text = response
            .text()
            .await
            .unwrap_or_else(|_| "未知错误".to_string());
        Err(format!("获取仓库列表失败: {}", error_text))
    }
}

#[tauri::command]
async fn get_projects(app_handle: tauri::AppHandle) -> Result<Vec<Project>, String> {
    // 这里应该从本地存储或配置文件中读取项目列表
    // 暂时返回空列表，后续可以实现持久化存储
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;
    let projects_path = app_dir.join("projects.json");

    if !projects_path.exists() {
        return Ok(vec![]);
    }

    let projects_json =
        fs::read_to_string(&projects_path).map_err(|e| format!("读取项目列表失败: {}", e))?;

    let projects: Vec<Project> =
        serde_json::from_str(&projects_json).map_err(|e| format!("解析项目列表失败: {}", e))?;

    Ok(projects)
}

#[tauri::command]
async fn save_projects(app_handle: tauri::AppHandle, projects: Vec<Project>) -> Result<(), String> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .map_err(|e| format!("无法获取应用数据目录: {}", e))?;
    let projects_path = app_dir.join("projects.json");

    // 确保目录存在
    if let Some(parent) = projects_path.parent() {
        fs::create_dir_all(parent).map_err(|e| format!("创建目录失败: {}", e))?;
    }

    let projects_json = serde_json::to_string_pretty(&projects)
        .map_err(|e| format!("序列化项目列表失败: {}", e))?;

    fs::write(&projects_path, projects_json).map_err(|e| format!("保存项目列表失败: {}", e))?;

    Ok(())
}

#[tauri::command]
async fn clone_repository(app_handle: tauri::AppHandle, repo_id: i64) -> Result<Project, String> {
    // 首先检查环境
    let env_status = check_environment().await?;
    if !env_status.git_installed {
        return Err("Git未安装，请先安装Git后再尝试克隆项目".to_string());
    }

    // 获取存储的凭证
    let credentials = match auth::load_gitea_credentials(app_handle.clone()).await {
        Ok(Some(creds)) => creds,
        Ok(None) => return Err("未找到登录凭证".to_string()),
        Err(e) => return Err(format!("加载凭证失败: {}", e)),
    };

    // 获取仓库信息
    let repos = fetch_gitea_repositories(app_handle.clone(), app_handle.state()).await?;
    let repo = repos
        .iter()
        .find(|r| r.id == repo_id)
        .ok_or("未找到指定的仓库")?;

    // 获取设置中的工作路径
    let settings = load_settings(app_handle.clone()).await?;
    let work_path = if settings.work_path.is_empty() {
        app_handle
            .path()
            .desktop_dir()
            .map_err(|e| format!("无法获取桌面路径: {}", e))?
            .join("CocosProjects")
    } else {
        PathBuf::from(&settings.work_path)
    };

    // 确保工作目录存在
    fs::create_dir_all(&work_path).map_err(|e| format!("创建工作目录失败: {}", e))?;

    let project_path = work_path.join(&repo.name);

    // 检查项目是否已存在
    if project_path.exists() {
        return Err(format!("项目 {} 已存在", repo.name));
    }

    // 执行git clone
    let output = Command::new("git")
        .args(&["clone", &repo.clone_url, project_path.to_str().unwrap()])
        .output()
        .map_err(|e| format!("执行git clone失败: {}", e))?;

    if !output.status.success() {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Git clone失败: {}", error_msg));
    }

    // 尝试获取项目的Cocos版本
    let cocos_version = get_project_cocos_version(project_path.to_string_lossy().to_string())
        .await
        .ok();

    // 创建项目记录
    let project = Project {
        id: format!("{}_{}", repo.id, chrono::Utc::now().timestamp()),
        name: repo.name.clone(),
        path: project_path.to_string_lossy().to_string(),
        status: "closed".to_string(),
        last_commit_time: chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
        repository_url: repo.clone_url.clone(),
        cocos_version,
        process_id: None,
    };

    // 保存项目到列表
    let mut projects = get_projects(app_handle.clone()).await?;
    projects.push(project.clone());
    save_projects(app_handle, projects).await?;

    Ok(project)
}

#[tauri::command]
async fn open_project(app_handle: tauri::AppHandle, project_id: String) -> Result<(), String> {
    // 获取项目列表
    let mut projects = get_projects(app_handle.clone()).await?;

    // 找到指定项目
    let project = projects
        .iter_mut()
        .find(|p| p.id == project_id)
        .ok_or("未找到指定的项目")?;

    // 检查项目路径是否存在
    let project_path = PathBuf::from(&project.path);
    if !project_path.exists() {
        return Err(format!("项目路径不存在: {}", project.path));
    }

    // 检查是否是Cocos Creator项目
    let project_json_path = project_path.join("project.json");
    if !project_json_path.exists() {
        return Err("这不是一个有效的Cocos Creator项目".to_string());
    }

    // 尝试启动Cocos Creator
    match launch_cocos_creator(app_handle.clone(), project_id.clone(), None).await {
        Ok(_) => {
            // Cocos Creator启动成功，项目状态已在launch_cocos_creator中更新
        }
        Err(_) => {
            // 如果启动Cocos Creator失败，回退到打开文件夹
            #[cfg(target_os = "windows")]
            {
                Command::new("explorer")
                    .arg(&project.path)
                    .spawn()
                    .map_err(|e| format!("打开项目文件夹失败: {}", e))?;
            }

            #[cfg(target_os = "macos")]
            {
                Command::new("open")
                    .arg(&project.path)
                    .spawn()
                    .map_err(|e| format!("打开项目文件夹失败: {}", e))?;
            }

            #[cfg(target_os = "linux")]
            {
                Command::new("xdg-open")
                    .arg(&project.path)
                    .spawn()
                    .map_err(|e| format!("打开项目文件夹失败: {}", e))?;
            }

            // 更新项目状态（文件夹打开方式）
            project.status = "opened_folder".to_string();
            project.last_commit_time = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

            // 保存更新后的项目列表
            save_projects(app_handle, projects).await?;
        }
    }

    Ok(())
}

#[tauri::command]
async fn remove_project(app_handle: tauri::AppHandle, project_id: String) -> Result<(), String> {
    // 获取项目列表
    let mut projects = get_projects(app_handle.clone()).await?;

    // 移除指定项目
    projects.retain(|p| p.id != project_id);

    // 保存更新后的项目列表
    save_projects(app_handle, projects).await?;

    Ok(())
}

#[tauri::command]
async fn check_git_status(project_path: String) -> Result<GitStatus, String> {
    let path = PathBuf::from(&project_path);
    if !path.exists() {
        return Err("项目路径不存在".to_string());
    }

    // 检查是否是git仓库
    let git_dir = path.join(".git");
    if !git_dir.exists() {
        return Err("这不是一个Git仓库".to_string());
    }

    // 获取git状态
    let output = Command::new("git")
        .args(&["status", "--porcelain"])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("执行git status失败: {}", e))?;

    if !output.status.success() {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        return Err(format!("Git status失败: {}", error_msg));
    }

    let status_output = String::from_utf8_lossy(&output.stdout);
    let has_changes = !status_output.trim().is_empty();

    // 获取当前分支
    let branch_output = Command::new("git")
        .args(&["branch", "--show-current"])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("获取分支信息失败: {}", e))?;

    let current_branch = if branch_output.status.success() {
        String::from_utf8_lossy(&branch_output.stdout)
            .trim()
            .to_string()
    } else {
        "unknown".to_string()
    };

    // 检查是否有远程更新
    let fetch_output = Command::new("git")
        .args(&["fetch", "--dry-run"])
        .current_dir(&path)
        .output();

    let has_remote_updates = match fetch_output {
        Ok(output) => !output.stderr.is_empty(),
        Err(_) => false,
    };

    Ok(GitStatus {
        has_changes,
        current_branch,
        has_remote_updates,
        status_output: status_output.to_string(),
    })
}

#[derive(Serialize, Deserialize, Clone)]
pub struct GitStatus {
    pub has_changes: bool,
    pub current_branch: String,
    pub has_remote_updates: bool,
    pub status_output: String,
}

#[tauri::command]
async fn commit_project_changes(
    project_path: String,
    commit_message: Option<String>,
) -> Result<String, String> {
    let path = PathBuf::from(&project_path);
    if !path.exists() {
        return Err("项目路径不存在".to_string());
    }

    // 检查是否是git仓库
    let git_dir = path.join(".git");
    if !git_dir.exists() {
        return Err("这不是一个Git仓库".to_string());
    }

    // 添加所有更改
    let add_output = Command::new("git")
        .args(&["add", "."])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("执行git add失败: {}", e))?;

    if !add_output.status.success() {
        let error_msg = String::from_utf8_lossy(&add_output.stderr);
        return Err(format!("Git add失败: {}", error_msg));
    }

    // 检查是否有内容需要提交
    let status_output = Command::new("git")
        .args(&["status", "--porcelain", "--cached"])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("检查暂存状态失败: {}", e))?;

    if status_output.status.success() && status_output.stdout.is_empty() {
        return Ok("没有需要提交的更改".to_string());
    }

    // 生成提交消息
    let message = commit_message.unwrap_or_else(|| {
        format!(
            "Auto commit - {}",
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
        )
    });

    // 执行提交
    let commit_output = Command::new("git")
        .args(&["commit", "-m", &message])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("执行git commit失败: {}", e))?;

    if !commit_output.status.success() {
        let error_msg = String::from_utf8_lossy(&commit_output.stderr);
        return Err(format!("Git commit失败: {}", error_msg));
    }

    let commit_result = String::from_utf8_lossy(&commit_output.stdout);
    Ok(format!("提交成功: {}", commit_result))
}

#[tauri::command]
async fn pull_project_updates(project_path: String) -> Result<String, String> {
    let path = PathBuf::from(&project_path);
    if !path.exists() {
        return Err("项目路径不存在".to_string());
    }

    // 检查是否是git仓库
    let git_dir = path.join(".git");
    if !git_dir.exists() {
        return Err("这不是一个Git仓库".to_string());
    }

    // 执行git pull
    let pull_output = Command::new("git")
        .args(&["pull"])
        .current_dir(&path)
        .output()
        .map_err(|e| format!("执行git pull失败: {}", e))?;

    if !pull_output.status.success() {
        let error_msg = String::from_utf8_lossy(&pull_output.stderr);
        return Err(format!("Git pull失败: {}", error_msg));
    }

    let pull_result = String::from_utf8_lossy(&pull_output.stdout);
    Ok(pull_result.to_string())
}

#[tauri::command]
async fn detect_cocos_creators() -> Result<Vec<CocosCreatorInfo>, String> {
    let mut creators = Vec::new();

    #[cfg(target_os = "windows")]
    {
        // Windows下检测Cocos Creator安装路径
        let common_paths = vec![
            r"C:\CocosCreator",
            r"D:\CocosCreator",
            r"E:\CocosCreator",
            r"C:\Program Files\CocosCreator",
            r"C:\Program Files (x86)\CocosCreator",
        ];

        for base_path in common_paths {
            let base_dir = PathBuf::from(base_path);
            if base_dir.exists() {
                if let Ok(entries) = fs::read_dir(&base_dir) {
                    for entry in entries.flatten() {
                        let path = entry.path();
                        if path.is_dir() {
                            let exe_path = path.join("CocosCreator.exe");
                            if exe_path.exists() {
                                if let Some(version) = extract_cocos_version(&path) {
                                    creators.push(CocosCreatorInfo {
                                        version,
                                        path: path.to_string_lossy().to_string(),
                                        executable: exe_path.to_string_lossy().to_string(),
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        // macOS下检测Cocos Creator
        let applications_path = PathBuf::from("/Applications");
        if let Ok(entries) = fs::read_dir(&applications_path) {
            for entry in entries.flatten() {
                let path = entry.path();
                if let Some(name) = path.file_name() {
                    if name.to_string_lossy().starts_with("CocosCreator") {
                        let exe_path = path.join("Contents/MacOS/CocosCreator");
                        if exe_path.exists() {
                            if let Some(version) = extract_cocos_version(&path) {
                                creators.push(CocosCreatorInfo {
                                    version,
                                    path: path.to_string_lossy().to_string(),
                                    executable: exe_path.to_string_lossy().to_string(),
                                });
                            }
                        }
                    }
                }
            }
        }
    }

    #[cfg(target_os = "linux")]
    {
        // Linux下检测Cocos Creator
        let home_dir = std::env::var("HOME").unwrap_or_default();
        let common_paths = vec![
            format!("{}/CocosCreator", home_dir),
            "/opt/CocosCreator".to_string(),
            "/usr/local/CocosCreator".to_string(),
        ];

        for base_path in common_paths {
            let base_dir = PathBuf::from(&base_path);
            if base_dir.exists() {
                if let Ok(entries) = fs::read_dir(&base_dir) {
                    for entry in entries.flatten() {
                        let path = entry.path();
                        if path.is_dir() {
                            let exe_path = path.join("CocosCreator");
                            if exe_path.exists() {
                                if let Some(version) = extract_cocos_version(&path) {
                                    creators.push(CocosCreatorInfo {
                                        version,
                                        path: path.to_string_lossy().to_string(),
                                        executable: exe_path.to_string_lossy().to_string(),
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 按版本排序
    creators.sort_by(|a, b| b.version.cmp(&a.version));

    Ok(creators)
}

fn extract_cocos_version(path: &PathBuf) -> Option<String> {
    // 尝试从路径名中提取版本号
    if let Some(name) = path.file_name() {
        let name_str = name.to_string_lossy();

        // 匹配版本号模式，如 "3.8.2", "2.4.13" 等
        let version_regex = Regex::new(r"(\d+\.\d+\.\d+)").ok()?;
        if let Some(captures) = version_regex.captures(&name_str) {
            return Some(captures.get(1)?.as_str().to_string());
        }
    }

    // 尝试从package.json或其他配置文件中读取版本信息
    let package_json = path.join("package.json");
    if package_json.exists() {
        if let Ok(content) = fs::read_to_string(&package_json) {
            if let Ok(json) = serde_json::from_str::<serde_json::Value>(&content) {
                if let Some(version) = json.get("version").and_then(|v| v.as_str()) {
                    return Some(version.to_string());
                }
            }
        }
    }

    None
}

#[tauri::command]
async fn get_project_cocos_version(project_path: String) -> Result<String, String> {
    let path = PathBuf::from(&project_path);
    let project_json = path.join("project.json");

    if !project_json.exists() {
        return Err("project.json文件不存在".to_string());
    }

    let content =
        fs::read_to_string(&project_json).map_err(|e| format!("读取project.json失败: {}", e))?;

    let json: serde_json::Value =
        serde_json::from_str(&content).map_err(|e| format!("解析project.json失败: {}", e))?;

    // 尝试获取引擎版本
    if let Some(engine) = json.get("engine") {
        if let Some(version) = engine.as_str() {
            return Ok(version.to_string());
        }
    }

    // 尝试从其他字段获取版本信息
    if let Some(version) = json.get("version").and_then(|v| v.as_str()) {
        return Ok(version.to_string());
    }

    Err("无法从project.json中获取引擎版本".to_string())
}

#[tauri::command]
async fn launch_cocos_creator(
    app_handle: tauri::AppHandle,
    project_id: String,
    creator_path: Option<String>,
) -> Result<(), String> {
    let app_state: State<AppState> = app_handle.state();

    // 获取项目信息
    let mut projects = get_projects(app_handle.clone()).await?;
    let project = projects
        .iter_mut()
        .find(|p| p.id == project_id)
        .ok_or("未找到指定的项目")?;

    let project_path = PathBuf::from(&project.path);
    if !project_path.exists() {
        return Err("项目路径不存在".to_string());
    }

    // 确定要使用的Cocos Creator路径
    let creator_executable = if let Some(path) = creator_path {
        path
    } else {
        // 自动选择合适的版本
        let creators = detect_cocos_creators().await?;
        if creators.is_empty() {
            return Err("未检测到Cocos Creator安装".to_string());
        }

        // 获取项目所需的版本
        let project_version = get_project_cocos_version(project.path.clone()).await.ok();

        // 尝试找到匹配的版本
        let selected_creator = if let Some(required_version) = &project_version {
            creators
                .iter()
                .find(|c| c.version.starts_with(required_version))
                .or_else(|| creators.first())
        } else {
            creators.first()
        };

        match selected_creator {
            Some(creator) => creator.executable.clone(),
            None => return Err("无法找到合适的Cocos Creator版本".to_string()),
        }
    };

    // 启动Cocos Creator
    let mut command = Command::new(&creator_executable);
    command.arg(&project.path);

    let child = command
        .spawn()
        .map_err(|e| format!("启动Cocos Creator失败: {}", e))?;

    // 存储进程信息
    let process_id = child.id();
    {
        let mut processes = app_state.running_processes.lock().unwrap();
        processes.insert(project_id.clone(), child);
    }

    // 更新项目状态
    project.status = "running".to_string();
    project.process_id = Some(process_id);
    project.last_commit_time = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();

    // 如果项目版本信息为空，尝试获取并保存
    if project.cocos_version.is_none() {
        if let Ok(version) = get_project_cocos_version(project.path.clone()).await {
            project.cocos_version = Some(version);
        }
    }

    save_projects(app_handle, projects).await?;

    Ok(())
}

#[tauri::command]
async fn check_project_process_status(
    app_handle: tauri::AppHandle,
    project_id: String,
) -> Result<bool, String> {
    let app_state: State<AppState> = app_handle.state();

    let mut processes = app_state.running_processes.lock().unwrap();

    if let Some(child) = processes.get_mut(&project_id) {
        // 检查进程是否还在运行
        match child.try_wait() {
            Ok(Some(_)) => {
                // 进程已结束
                processes.remove(&project_id);

                // 更新项目状态
                let mut projects = get_projects(app_handle.clone()).await?;
                if let Some(project) = projects.iter_mut().find(|p| p.id == project_id) {
                    project.status = "closed".to_string();
                    project.process_id = None;
                    save_projects(app_handle, projects).await?;
                }

                Ok(false)
            }
            Ok(None) => {
                // 进程仍在运行
                Ok(true)
            }
            Err(e) => {
                // 检查进程状态时出错
                processes.remove(&project_id);
                Err(format!("检查进程状态失败: {}", e))
            }
        }
    } else {
        Ok(false)
    }
}

#[tauri::command]
async fn stop_project_process(
    app_handle: tauri::AppHandle,
    project_id: String,
) -> Result<(), String> {
    let app_state: State<AppState> = app_handle.state();

    let mut processes = app_state.running_processes.lock().unwrap();

    if let Some(mut child) = processes.remove(&project_id) {
        // 尝试优雅地终止进程
        match child.kill() {
            Ok(_) => {
                // 等待进程结束
                let _ = child.wait();

                // 更新项目状态
                let mut projects = get_projects(app_handle.clone()).await?;
                if let Some(project) = projects.iter_mut().find(|p| p.id == project_id) {
                    project.status = "closed".to_string();
                    project.process_id = None;
                    save_projects(app_handle, projects).await?;
                }

                Ok(())
            }
            Err(e) => Err(format!("终止进程失败: {}", e)),
        }
    } else {
        Err("未找到运行中的进程".to_string())
    }
}

#[tauri::command]
async fn get_running_projects_status(app_handle: tauri::AppHandle) -> Result<Vec<Project>, String> {
    let app_state: State<AppState> = app_handle.state();
    let mut projects = get_projects(app_handle.clone()).await?;
    let mut processes = app_state.running_processes.lock().unwrap();
    let mut updated = false;

    // 检查所有运行中的项目状态
    for project in projects.iter_mut() {
        if project.status == "running" {
            if let Some(child) = processes.get_mut(&project.id) {
                match child.try_wait() {
                    Ok(Some(_)) => {
                        // 进程已结束
                        project.status = "closed".to_string();
                        project.process_id = None;
                        processes.remove(&project.id);
                        updated = true;
                    }
                    Ok(None) => {
                        // 进程仍在运行，保持状态
                    }
                    Err(_) => {
                        // 检查失败，假设进程已结束
                        project.status = "closed".to_string();
                        project.process_id = None;
                        processes.remove(&project.id);
                        updated = true;
                    }
                }
            } else {
                // 没有对应的进程记录，更新状态
                project.status = "closed".to_string();
                project.process_id = None;
                updated = true;
            }
        }
    }

    // 如果有状态更新，保存项目列表
    if updated {
        save_projects(app_handle, projects.clone()).await?;
    }

    Ok(projects)
}

#[tauri::command]
async fn send_notification(title: String, body: String) -> Result<(), String> {
    use tauri_plugin_notification::NotificationExt;

    // 这里可以根据设置选择不同的通知方式
    // 目前先实现系统通知

    // 发送系统通知
    if let Err(e) = tauri::api::notification::Notification::new("cocos-initiator")
        .title(&title)
        .body(&body)
        .show()
    {
        return Err(format!("发送通知失败: {}", e));
    }

    Ok(())
}

#[tauri::command]
async fn send_wechat_notification(webhook_url: String, message: String) -> Result<(), String> {
    let client = reqwest::Client::new();

    let payload = serde_json::json!({
        "msgtype": "text",
        "text": {
            "content": message
        }
    });

    let response = client
        .post(&webhook_url)
        .json(&payload)
        .send()
        .await
        .map_err(|e| format!("发送微信通知失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("微信通知发送失败: {}", response.status()));
    }

    Ok(())
}

#[tauri::command]
async fn send_feishu_notification(webhook_url: String, message: String) -> Result<(), String> {
    let client = reqwest::Client::new();

    let payload = serde_json::json!({
        "msg_type": "text",
        "content": {
            "text": message
        }
    });

    let response = client
        .post(&webhook_url)
        .json(&payload)
        .send()
        .await
        .map_err(|e| format!("发送飞书通知失败: {}", e))?;

    if !response.status().is_success() {
        return Err(format!("飞书通知发送失败: {}", response.status()));
    }

    Ok(())
}

#[tauri::command]
async fn test_notification_settings(app_handle: tauri::AppHandle) -> Result<(), String> {
    let settings = load_settings(app_handle).await?;

    let test_message = "这是一条测试通知消息";

    match settings.notification_type.as_str() {
        "wechat" => {
            if !settings.wechat_config.is_empty() {
                send_wechat_notification(settings.wechat_config, test_message.to_string()).await?;
            } else {
                return Err("微信配置为空".to_string());
            }
        }
        "feishu" => {
            if !settings.feishu_config.is_empty() {
                send_feishu_notification(settings.feishu_config, test_message.to_string()).await?;
            } else {
                return Err("飞书配置为空".to_string());
            }
        }
        "system" | _ => {
            send_notification("Cocos Initiator".to_string(), test_message.to_string()).await?;
        }
    }

    Ok(())
}

#[tauri::command]
async fn show_main_window(app_handle: tauri::AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_window("main") {
        window.show().map_err(|e| format!("显示窗口失败: {}", e))?;
        window
            .set_focus()
            .map_err(|e| format!("聚焦窗口失败: {}", e))?;
    }
    Ok(())
}

#[tauri::command]
async fn hide_main_window(app_handle: tauri::AppHandle) -> Result<(), String> {
    if let Some(window) = app_handle.get_window("main") {
        window.hide().map_err(|e| format!("隐藏窗口失败: {}", e))?;
    }
    Ok(())
}

#[tauri::command]
async fn check_for_updates() -> Result<String, String> {
    // 这里实现更新检查逻辑
    // 暂时返回模拟结果
    Ok("当前已是最新版本".to_string())
}

#[tauri::command]
async fn register_context_menu() -> Result<(), String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;

        // 获取当前可执行文件路径
        let exe_path =
            std::env::current_exe().map_err(|e| format!("获取可执行文件路径失败: {}", e))?;

        let exe_path_str = exe_path.to_string_lossy();

        // 注册右键菜单项
        let commands = vec![
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\shell\CocosInitiator" /ve /d "用Cocos Initiator打开" /f"#
            ),
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\shell\CocosInitiator" /v "Icon" /d "{}" /f"#,
                exe_path_str
            ),
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\shell\CocosInitiator\command" /ve /d "\"{}\" \"%1\"" /f"#,
                exe_path_str
            ),
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\Background\shell\CocosInitiator" /ve /d "用Cocos Initiator打开" /f"#
            ),
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\Background\shell\CocosInitiator" /v "Icon" /d "{}" /f"#,
                exe_path_str
            ),
            format!(
                r#"reg add "HKEY_CLASSES_ROOT\Directory\Background\shell\CocosInitiator\command" /ve /d "\"{}\" \"%V\"" /f"#,
                exe_path_str
            ),
        ];

        for cmd in commands {
            let output = Command::new("cmd")
                .args(&["/C", &cmd])
                .output()
                .map_err(|e| format!("执行注册命令失败: {}", e))?;

            if !output.status.success() {
                let error_msg = String::from_utf8_lossy(&output.stderr);
                return Err(format!("注册右键菜单失败: {}", error_msg));
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        // macOS 的右键菜单注册需要通过 Automator 或其他方式
        // 这里暂时返回不支持的提示
        return Err("macOS 暂不支持自动注册右键菜单".to_string());
    }

    #[cfg(target_os = "linux")]
    {
        // Linux 的右键菜单注册需要创建 .desktop 文件
        // 这里暂时返回不支持的提示
        return Err("Linux 暂不支持自动注册右键菜单".to_string());
    }

    Ok(())
}

#[tauri::command]
async fn unregister_context_menu() -> Result<(), String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;

        let commands = vec![
            r#"reg delete "HKEY_CLASSES_ROOT\Directory\shell\CocosInitiator" /f"#,
            r#"reg delete "HKEY_CLASSES_ROOT\Directory\Background\shell\CocosInitiator" /f"#,
        ];

        for cmd in commands {
            let output = Command::new("cmd")
                .args(&["/C", cmd])
                .output()
                .map_err(|e| format!("执行注销命令失败: {}", e))?;

            // 忽略删除不存在项的错误
            if !output.status.success() {
                let error_msg = String::from_utf8_lossy(&output.stderr);
                if !error_msg.contains("找不到指定的注册表项或值") {
                    return Err(format!("注销右键菜单失败: {}", error_msg));
                }
            }
        }
    }

    #[cfg(not(target_os = "windows"))]
    {
        return Err("当前系统暂不支持自动注销右键菜单".to_string());
    }

    Ok(())
}

#[tauri::command]
async fn check_context_menu_status() -> Result<bool, String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;

        let output = Command::new("cmd")
            .args(&[
                "/C",
                r#"reg query "HKEY_CLASSES_ROOT\Directory\shell\CocosInitiator""#,
            ])
            .output()
            .map_err(|e| format!("检查注册表失败: {}", e))?;

        Ok(output.status.success())
    }

    #[cfg(not(target_os = "windows"))]
    {
        Ok(false)
    }
}

#[tauri::command]
async fn check_environment() -> Result<EnvironmentStatus, String> {
    let mut env_status = EnvironmentStatus {
        git_installed: false,
        git_version: None,
        nodejs_installed: false,
        nodejs_version: None,
        npm_installed: false,
        npm_version: None,
        all_ready: false,
    };

    // 检查Git
    match Command::new("git").args(&["--version"]).output() {
        Ok(output) if output.status.success() => {
            env_status.git_installed = true;
            let version_output = String::from_utf8_lossy(&output.stdout);
            // 提取版本号，格式通常是 "git version 2.x.x"
            if let Some(version) = version_output.split_whitespace().nth(2) {
                env_status.git_version = Some(version.to_string());
            }
        }
        _ => {
            env_status.git_installed = false;
        }
    }

    // 检查Node.js
    match Command::new("node").args(&["--version"]).output() {
        Ok(output) if output.status.success() => {
            env_status.nodejs_installed = true;
            let version_output = String::from_utf8_lossy(&output.stdout).trim().to_string();
            env_status.nodejs_version = Some(version_output);
        }
        _ => {
            env_status.nodejs_installed = false;
        }
    }

    // 检查npm
    match Command::new("npm").args(&["--version"]).output() {
        Ok(output) if output.status.success() => {
            env_status.npm_installed = true;
            let version_output = String::from_utf8_lossy(&output.stdout).trim().to_string();
            env_status.npm_version = Some(version_output);
        }
        _ => {
            env_status.npm_installed = false;
        }
    }

    // 判断环境是否完整
    env_status.all_ready = env_status.git_installed && env_status.nodejs_installed;

    Ok(env_status)
}

#[tauri::command]
async fn open_installation_guide(software: String) -> Result<(), String> {
    let url = match software.as_str() {
        "git" => "https://git-scm.com/downloads",
        "nodejs" => "https://nodejs.org/",
        _ => return Err("未知的软件类型".to_string()),
    };

    #[cfg(target_os = "windows")]
    {
        Command::new("cmd")
            .args(&["/C", "start", url])
            .spawn()
            .map_err(|e| format!("打开安装指南失败: {}", e))?;
    }

    #[cfg(target_os = "macos")]
    {
        Command::new("open")
            .arg(url)
            .spawn()
            .map_err(|e| format!("打开安装指南失败: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        Command::new("xdg-open")
            .arg(url)
            .spawn()
            .map_err(|e| format!("打开安装指南失败: {}", e))?;
    }

    Ok(())
}

fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示主窗口");
    let hide = CustomMenuItem::new("hide".to_string(), "隐藏到托盘");
    let workspace = CustomMenuItem::new("workspace".to_string(), "打开工作区");
    let cocos_manager = CustomMenuItem::new("cocos_manager".to_string(), "Cocos管理");

    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(workspace)
        .add_item(cocos_manager)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}

fn handle_system_tray_event(app: &AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick {
            position: _,
            size: _,
            ..
        } => {
            // 左键点击显示主窗口
            if let Some(window) = app.get_window("main") {
                let _ = window.show();
                let _ = window.set_focus();
            }
        }
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    if let Some(window) = app.get_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                    }
                }
                "hide" => {
                    if let Some(window) = app.get_window("main") {
                        let _ = window.hide();
                    }
                }
                "workspace" => {
                    if let Some(window) = app.get_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                        // 这里可以发送事件到前端导航到工作区
                        let _ = window.emit("navigate", "/workspace");
                    }
                }
                "cocos_manager" => {
                    if let Some(window) = app.get_window("main") {
                        let _ = window.show();
                        let _ = window.set_focus();
                        // 这里可以发送事件到前端导航到Cocos管理页面
                        let _ = window.emit("navigate", "/cocos-manager");
                    }
                }
                _ => {}
            }
        }
        _ => {}
    }
}

fn main() {
    // 创建系统托盘图标
    let tray_icon = TrayIconBuilder::new()
        .on_click(|app| {
            println!("托盘图标被点击");
            // 显示主窗口
            if let Some(window) = app.get_window("main") {
                window.show().unwrap();
                window.set_focus().unwrap();
            }
        })
        .build()
        .unwrap();

    tauri::Builder::default()
        .plugin(tauri_plugin_store::Builder::default().build())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_updater::Builder::new().build())
        .plugin(tauri_plugin_process::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_tray_icon::Builder::new().build())
        .manage(tray_icon)
        .on_tray_event(|app, event| {
            match event {
                TrayIconEvent::MenuItemClick { id, .. } => {
                    match id.as_str() {
                        "quit" => {
                            std::process::exit(0);
                        }
                        "toggle" => {
                            if let Some(window) = app.get_window("main") {
                                if window.is_visible().unwrap() {
                                    window.hide().unwrap();
                                } else {
                                    window.show().unwrap();
                                    window.set_focus().unwrap();
                                }
                            }
                        }
                        _ => {}
                    }
                }
                _ => {}
            }
        })
        .setup(|app| {
            let app_handle = app.handle();
            let config = AppConfig {
                gitea_api_base_url: "https://code.judugame.online/api/v1".to_string(),
            };
            app.manage(AppState {
                config: Mutex::new(config),
                running_processes: Mutex::new(HashMap::new()),
            });
            
            // 设置窗口关闭行为（隐藏到托盘而不是退出）
            let window = app.get_window("main").unwrap();
            window.on_window_event(move |event| {
                match event {
                    tauri::WindowEvent::CloseRequested { api, .. } => {
                        // 阻止默认关闭行为
                        api.prevent_close();
                        // 隐藏窗口到托盘
                        if let Some(window) = app_handle.get_window("main") {
                            let _ = window.hide();
                        }
                    }
                    _ => {}
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            gitea_login_command,
            load_gitea_credentials,
            clear_gitea_credentials,
            load_settings,
            save_settings,
            select_folder,
            load_app_config,
            fetch_gitea_repositories,
            get_projects,
            save_projects,
            clone_repository,
            open_project,
            remove_project,
            check_git_status,
            commit_project_changes,
            pull_project_updates,
            detect_cocos_creators,
            get_project_cocos_version,
            launch_cocos_creator,
            check_project_process_status,
            stop_project_process,
            get_running_projects_status,
            send_notification,
            send_wechat_notification,
            send_feishu_notification,
            test_notification_settings,
            show_main_window,
            hide_main_window,
            check_for_updates,
            register_context_menu,
            unregister_context_menu,
            check_context_menu_status,
            check_environment,
            open_installation_guide
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
