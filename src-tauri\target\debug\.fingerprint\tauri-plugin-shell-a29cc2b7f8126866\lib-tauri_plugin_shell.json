{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 2911265107896841603, "deps": [[500211409582349667, "shared_child", false, 1196804411474385896], [1582828171158827377, "build_script_build", false, 17916824361942114591], [5986029879202738730, "log", false, 8555561636712512847], [9451456094439810778, "regex", false, 3815492220500898872], [9538054652646069845, "tokio", false, 8982450163724697713], [9689903380558560274, "serde", false, 12420880179438694917], [10755362358622467486, "tauri", false, 16350496299621225213], [10806645703491011684, "thiserror", false, 3881699408214437866], [11337703028400419576, "os_pipe", false, 4627484228142062159], [14564311161534545801, "encoding_rs", false, 71011171848525825], [15367738274754116744, "serde_json", false, 17720886181995014648], [16192041687293812804, "open", false, 12626866592013184866]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-a29cc2b7f8126866\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}