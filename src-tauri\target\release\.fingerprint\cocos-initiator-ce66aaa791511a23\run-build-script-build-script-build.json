{"rustc": 16591470773350601817, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[2983311369882895114, "build_script_build", false, 13081692795823640800], [10755362358622467486, "build_script_build", false, 12242051209942776525], [12783828711503588811, "build_script_build", false, 6989816878674569119], [17509843537913359226, "build_script_build", false, 14204686493991180777], [1582828171158827377, "build_script_build", false, 6760108438678220053], [5943080732378436368, "build_script_build", false, 14721521667491149027], [11721252211900136025, "build_script_build", false, 9817109601732795452]], "local": [{"RerunIfChanged": {"output": "release\\build\\cocos-initiator-ce66aaa791511a23\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}