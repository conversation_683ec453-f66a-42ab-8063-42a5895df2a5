{"rustc": 16591470773350601817, "features": "[\"http1\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 632669836025513150, "deps": [[784494742817713399, "tower_service", false, 4279352996323888024], [970965535607393401, "hyper_util", false, 5624277221676450509], [2883436298747778685, "pki_types", false, 3326270106593689579], [4942430025333810336, "webpki_roots", false, 15528953474044521003], [7161480121686072451, "rustls", false, 4731202655510400510], [9010263965687315507, "http", false, 13891704466643743123], [9538054652646069845, "tokio", false, 8982450163724697713], [11895591994124935963, "tokio_rustls", false, 6428660087443504891], [11957360342995674422, "hyper", false, 874630870184600653]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-905eef262d00b38b\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}