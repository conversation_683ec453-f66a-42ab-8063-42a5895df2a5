{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12844171384082609526, "deps": [[4899080583175475170, "semver", false, 5608513836075258160], [6913375703034175521, "schemars", false, 11519239817704840179], [7170110829644101142, "json_patch", false, 6566727955510569424], [8786711029710048183, "toml", false, 12089303468504858911], [9689903380558560274, "serde", false, 15168726803780673892], [11050281405049894993, "tauri_utils", false, 14405284534638119549], [12714016054753183456, "tauri_winres", false, 1703917702285188722], [13077543566650298139, "heck", false, 2225206494654259885], [13475171727366188400, "cargo_toml", false, 2405074069839714088], [13625485746686963219, "anyhow", false, 9739582024938941695], [15367738274754116744, "serde_json", false, 16047163076923800125], [15622660310229662834, "walkdir", false, 11931754989844931061], [16928111194414003569, "dirs", false, 14965473551192393748], [17155886227862585100, "glob", false, 8808646221422140041]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-d9280f184004d6d6\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}