{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 17231900515118247201, "deps": [[376837177317575824, "softbuffer", false, 16458667306440106727], [442785307232013896, "tauri_runtime", false, 3954063056236084616], [3150220818285335163, "url", false, 7454335481254515655], [3722963349756955755, "once_cell", false, 9072542997806263575], [4143744114649553716, "raw_window_handle", false, 2995522544373100678], [5986029879202738730, "log", false, 8555561636712512847], [7752760652095876438, "build_script_build", false, 11564509335214606492], [8539587424388551196, "webview2_com", false, 4618949674958641600], [9010263965687315507, "http", false, 13891704466643743123], [11050281405049894993, "tauri_utils", false, 5826606491688714813], [13116089016666501665, "windows", false, 15784846249245364666], [13223659721939363523, "tao", false, 10807986788654875196], [14794439852947137341, "wry", false, 1931630123982997830]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-de70b05baf3cacbb\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}