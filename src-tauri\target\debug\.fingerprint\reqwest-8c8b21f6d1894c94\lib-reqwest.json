{"rustc": 16591470773350601817, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"json\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 11573281436853214370, "deps": [[40386456601120721, "percent_encoding", false, 9903042276772267709], [95042085696191081, "ipnet", false, 987480743821075565], [784494742817713399, "tower_service", false, 4279352996323888024], [970965535607393401, "hyper_util", false, 5624277221676450509], [1288403060204016458, "tokio_util", false, 12623025856854550760], [1475350004718460454, "tower_http", false, 8354072765671204387], [1788832197870803419, "hyper_rustls", false, 7851301116415365272], [1811549171721445101, "futures_channel", false, 10050798766872274183], [1906322745568073236, "pin_project_lite", false, 5517959609193328240], [2517136641825875337, "sync_wrapper", false, 13058962405140814127], [2883436298747778685, "rustls_pki_types", false, 3326270106593689579], [3150220818285335163, "url", false, 7454335481254515655], [3722963349756955755, "once_cell", false, 9072542997806263575], [4942430025333810336, "webpki_roots", false, 15528953474044521003], [5695049318159433696, "tower", false, 12795875158302883296], [5986029879202738730, "log", false, 8555561636712512847], [7161480121686072451, "rustls", false, 4731202655510400510], [7620660491849607393, "futures_core", false, 15145191665480823398], [9010263965687315507, "http", false, 13891704466643743123], [9538054652646069845, "tokio", false, 8982450163724697713], [9689903380558560274, "serde", false, 12420880179438694917], [10229185211513642314, "mime", false, 1852428248072666615], [10629569228670356391, "futures_util", false, 383300555520732793], [11895591994124935963, "tokio_rustls", false, 6428660087443504891], [11957360342995674422, "hyper", false, 874630870184600653], [13077212702700853852, "base64", false, 13674227561419837107], [14084095096285906100, "http_body", false, 10916235814274115685], [15367738274754116744, "serde_json", false, 17720886181995014648], [16066129441945555748, "bytes", false, 11196354275054407066], [16542808166767769916, "serde_urlencoded", false, 1826467178840019371], [16900715236047033623, "http_body_util", false, 9099864671971961575]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-8c8b21f6d1894c94\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}