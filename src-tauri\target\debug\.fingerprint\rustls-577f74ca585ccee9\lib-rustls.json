{"rustc": 16591470773350601817, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 17610330594132601694, "deps": [[2883436298747778685, "pki_types", false, 3326270106593689579], [3722963349756955755, "once_cell", false, 9072542997806263575], [5491919304041016563, "ring", false, 17467581341565895108], [6528079939221783635, "zeroize", false, 15300614510429924654], [7161480121686072451, "build_script_build", false, 18230988269031302089], [17003143334332120809, "subtle", false, 181218399105646356], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 18025628127105512640]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-577f74ca585ccee9\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}