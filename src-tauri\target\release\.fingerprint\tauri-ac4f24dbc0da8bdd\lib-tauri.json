{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 367816849085071872, "path": 2484673656501667652, "deps": [[40386456601120721, "percent_encoding", false, 12596436445136119614], [442785307232013896, "tauri_runtime", false, 8356745966971955152], [1200537532907108615, "url<PERSON><PERSON>n", false, 3759044069293431078], [3150220818285335163, "url", false, 17986292695802632123], [4143744114649553716, "raw_window_handle", false, 14973095541760575874], [4341921533227644514, "muda", false, 12612048410267155243], [4919829919303820331, "serialize_to_javascript", false, 13011894689738776068], [5986029879202738730, "log", false, 82691605009859048], [7752760652095876438, "tauri_runtime_wry", false, 5185977796351750408], [8539587424388551196, "webview2_com", false, 327719958832832374], [9010263965687315507, "http", false, 7923552186516675881], [9228235415475680086, "tauri_macros", false, 2157155021020087895], [9538054652646069845, "tokio", false, 17242659437566319771], [9689903380558560274, "serde", false, 7153253516477134116], [9920160576179037441, "getrandom", false, 4914809902468307578], [10229185211513642314, "mime", false, 1622292474083181368], [10629569228670356391, "futures_util", false, 1396026813022400302], [10755362358622467486, "build_script_build", false, 12242051209942776525], [10806645703491011684, "thiserror", false, 5976101502267111473], [11050281405049894993, "tauri_utils", false, 16794711885998847944], [11989259058781683633, "dunce", false, 2495506939808963512], [12565293087094287914, "window_vibrancy", false, 3224687364607414615], [12986574360607194341, "serde_repr", false, 7748733700478918286], [13077543566650298139, "heck", false, 11168763813866766655], [13116089016666501665, "windows", false, 12252872986836496617], [13625485746686963219, "anyhow", false, 2115109158373166646], [15367738274754116744, "serde_json", false, 11919080132714597288], [16928111194414003569, "dirs", false, 17447279571772622498], [17155886227862585100, "glob", false, 5391393751989952526]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-ac4f24dbc0da8bdd\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}