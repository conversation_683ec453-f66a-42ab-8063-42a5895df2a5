# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-maximized"
description = "Enables the is_maximized command without any pre-configured scope."
commands.allow = ["is_maximized"]

[[permission]]
identifier = "deny-is-maximized"
description = "Denies the is_maximized command without any pre-configured scope."
commands.deny = ["is_maximized"]
