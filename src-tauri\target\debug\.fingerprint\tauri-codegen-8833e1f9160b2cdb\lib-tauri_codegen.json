{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 8985965904563823836, "deps": [[3060637413840920116, "proc_macro2", false, 3722225902823197376], [3150220818285335163, "url", false, 13355888952511495717], [4899080583175475170, "semver", false, 5608513836075258160], [7170110829644101142, "json_patch", false, 6566727955510569424], [7392050791754369441, "ico", false, 12540923445163094327], [8319709847752024821, "uuid", false, 4738604866675565772], [9689903380558560274, "serde", false, 15168726803780673892], [9857275760291862238, "sha2", false, 4078053100786685821], [10806645703491011684, "thiserror", false, 3881699408214437866], [11050281405049894993, "tauri_utils", false, 14405284534638119549], [12687914511023397207, "png", false, 10513801603899320825], [13077212702700853852, "base64", false, 13674227561419837107], [14132538657330703225, "brotli", false, 4246135052404262665], [15367738274754116744, "serde_json", false, 16047163076923800125], [15622660310229662834, "walkdir", false, 11931754989844931061], [17990358020177143287, "quote", false, 15042609247914122772], [18149961000318489080, "syn", false, 6471091054225964174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-8833e1f9160b2cdb\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}