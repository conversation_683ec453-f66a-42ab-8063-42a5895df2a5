["\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\E:\\share\\code\\cocos-Initiator\\src-tauri\\target\\release\\build\\tauri-e4cbc4413b995d18\\out\\permissions\\path\\autogenerated\\default.toml"]