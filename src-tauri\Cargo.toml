[package]
name = "cocos-initiator"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "cocos_initiator_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.12", features = ["json", "blocking"], default-features = false }
tauri-plugin-store = { version = "2", features = [] }
anyhow = "1.0"
tokio = { version = "1", features = ["full"] }
sysinfo = "0.30"
chrono = { version = "0.4", features = ["serde"] }
regex = "1.0"
tauri-plugin-notification = "2.0"
tauri-plugin-updater = "2.0"
tauri-plugin-shell = "2.0"
tauri-plugin-process = "2.0"  
tauri-plugin-tray = { version = "2.0.0-beta.10", features = ["serde"] }


[profile.dev]
incremental = true # 以较小的步骤编译您的二进制文件。

[profile.release]
codegen-units = 1 # 允许 LLVM 执行更好的优化。
lto = true # 启用链接时优化。
opt-level = "s" # 优先考虑小的二进制文件大小。如果您更喜欢速度，请使用 `3`。
panic = "abort" # 通过禁用 panic 处理程序来提高性能。
strip = true # 确保移除调试符号。
