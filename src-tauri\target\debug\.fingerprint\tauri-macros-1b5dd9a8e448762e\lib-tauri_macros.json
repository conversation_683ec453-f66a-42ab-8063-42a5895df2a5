{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 8778928345189965635, "deps": [[3060637413840920116, "proc_macro2", false, 3722225902823197376], [7341521034400937459, "tauri_codegen", false, 359017425427129440], [11050281405049894993, "tauri_utils", false, 14405284534638119549], [13077543566650298139, "heck", false, 2225206494654259885], [17990358020177143287, "quote", false, 15042609247914122772], [18149961000318489080, "syn", false, 6471091054225964174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-1b5dd9a8e448762e\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}